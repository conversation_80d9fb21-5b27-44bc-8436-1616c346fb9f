"""
Excel generator for creating organized workbooks from transaction data.
"""

from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime, date
from decimal import Decimal
import pandas as pd
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.formatting.rule import CellIsRule
from openpyxl.utils.dataframe import dataframe_to_rows

from config.settings import get_settings
from models.transaction import Transaction, TransactionBatch
from utils.logger import get_logger, log_execution_time


class ExcelGenerator:
    """Generator for Excel workbooks with proper formatting."""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger(__name__)
        
        # Define styles
        self.styles = {
            'header': {
                'font': Font(bold=True, color='FFFFFF'),
                'fill': Pattern<PERSON>ill(start_color='366092', end_color='366092', fill_type='solid'),
                'alignment': Alignment(horizontal='center', vertical='center')
            },
            'debit': {
                'font': Font(color='CC0000'),  # Red for debits
                'fill': PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
            },
            'credit': {
                'font': Font(color='008000'),  # Green for credits
                'fill': PatternFill(start_color='E6FFE6', end_color='E6FFE6', fill_type='solid')
            },
            'summary': {
                'font': Font(bold=True),
                'fill': PatternFill(start_color='F0F0F0', end_color='F0F0F0', fill_type='solid')
            }
        }
        
        # Define borders
        self.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
    
    def generate_workbook(
        self, 
        transaction_batches: List[TransactionBatch], 
        output_path: Path,
        separate_banks: bool = None,
        monthly_sheets: bool = None
    ) -> Dict[str, Any]:
        """Generate Excel workbook from transaction batches."""
        
        if separate_banks is None:
            separate_banks = self.settings.separate_banks
        if monthly_sheets is None:
            monthly_sheets = self.settings.monthly_sheets
        
        with log_execution_time(self.logger, "generate_excel_workbook"):
            try:
                if separate_banks:
                    return self._generate_separate_bank_workbooks(
                        transaction_batches, output_path, monthly_sheets
                    )
                else:
                    return self._generate_single_workbook(
                        transaction_batches, output_path, monthly_sheets
                    )
            
            except Exception as e:
                self.logger.error(f"Error generating Excel workbook: {e}")
                return {
                    'success': False,
                    'files_created': [],
                    'error': str(e)
                }
    
    def _generate_separate_bank_workbooks(
        self, 
        transaction_batches: List[TransactionBatch], 
        output_path: Path,
        monthly_sheets: bool
    ) -> Dict[str, Any]:
        """Generate separate workbooks for each bank."""
        
        # Group batches by bank
        bank_batches = {}
        for batch in transaction_batches:
            bank_name = batch.bank_name or 'Unknown_Bank'
            bank_name = self._sanitize_filename(bank_name)
            
            if bank_name not in bank_batches:
                bank_batches[bank_name] = []
            bank_batches[bank_name].append(batch)
        
        files_created = []
        
        for bank_name, batches in bank_batches.items():
            try:
                # Create workbook for this bank
                workbook_path = output_path / f"{bank_name}_Statements.xlsx"
                result = self._create_workbook(batches, workbook_path, monthly_sheets)
                
                if result['success']:
                    files_created.append(str(workbook_path))
                    self.logger.info(f"Created workbook for {bank_name}: {workbook_path}")
                else:
                    self.logger.error(f"Failed to create workbook for {bank_name}: {result['error']}")
            
            except Exception as e:
                self.logger.error(f"Error creating workbook for {bank_name}: {e}")
        
        return {
            'success': len(files_created) > 0,
            'files_created': files_created,
            'error': None if files_created else "No workbooks created successfully"
        }
    
    def _generate_single_workbook(
        self, 
        transaction_batches: List[TransactionBatch], 
        output_path: Path,
        monthly_sheets: bool
    ) -> Dict[str, Any]:
        """Generate single workbook with all transactions."""
        
        workbook_path = output_path / "All_Bank_Statements.xlsx"
        result = self._create_workbook(transaction_batches, workbook_path, monthly_sheets)
        
        if result['success']:
            return {
                'success': True,
                'files_created': [str(workbook_path)],
                'error': None
            }
        else:
            return {
                'success': False,
                'files_created': [],
                'error': result['error']
            }
    
    def _create_workbook(
        self, 
        transaction_batches: List[TransactionBatch], 
        workbook_path: Path,
        monthly_sheets: bool
    ) -> Dict[str, Any]:
        """Create Excel workbook with transaction data."""
        
        try:
            # Ensure output directory exists
            workbook_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Combine all transactions
            all_transactions = []
            for batch in transaction_batches:
                all_transactions.extend(batch.transactions)
            
            if not all_transactions:
                return {
                    'success': False,
                    'error': 'No transactions to write'
                }
            
            # Create workbook
            wb = Workbook()
            
            # Remove default sheet
            if 'Sheet' in wb.sheetnames:
                wb.remove(wb['Sheet'])
            
            if monthly_sheets:
                self._create_monthly_sheets(wb, all_transactions)
            else:
                self._create_single_sheet(wb, all_transactions, "All Transactions")
            
            # Add summary sheet if enabled
            if self.settings.include_summary:
                self._create_summary_sheet(wb, transaction_batches, all_transactions)
            
            # Save workbook
            wb.save(workbook_path)
            
            return {
                'success': True,
                'error': None
            }
        
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _create_monthly_sheets(self, workbook: Workbook, transactions: List[Transaction]):
        """Create separate sheets for each month."""
        
        # Group transactions by month
        monthly_transactions = {}
        for transaction in transactions:
            month_key = transaction.date.strftime('%Y-%m')
            month_name = transaction.date.strftime('%B %Y')
            
            if month_key not in monthly_transactions:
                monthly_transactions[month_key] = {
                    'name': month_name,
                    'transactions': []
                }
            monthly_transactions[month_key]['transactions'].append(transaction)
        
        # Create sheets for each month
        for month_key in sorted(monthly_transactions.keys()):
            month_data = monthly_transactions[month_key]
            sheet_name = self._sanitize_sheet_name(month_data['name'])
            
            self._create_single_sheet(workbook, month_data['transactions'], sheet_name)
    
    def _create_single_sheet(self, workbook: Workbook, transactions: List[Transaction], sheet_name: str):
        """Create a single sheet with transaction data."""
        
        # Create worksheet
        ws = workbook.create_sheet(title=sheet_name)
        
        # Define headers
        headers = ['Date', 'Description', 'Debit', 'Credit', 'Balance']
        
        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = self.styles['header']['font']
            cell.fill = self.styles['header']['fill']
            cell.alignment = self.styles['header']['alignment']
            cell.border = self.border
        
        # Write transaction data
        for row, transaction in enumerate(transactions, 2):
            # Date
            ws.cell(row=row, column=1, value=transaction.date.strftime('%d/%m/%Y')).border = self.border
            
            # Description
            ws.cell(row=row, column=2, value=transaction.description).border = self.border
            
            # Debit
            debit_cell = ws.cell(row=row, column=3)
            if transaction.debit:
                debit_cell.value = float(transaction.debit)
                debit_cell.font = self.styles['debit']['font']
                debit_cell.fill = self.styles['debit']['fill']
                debit_cell.number_format = '#,##0.00'
            debit_cell.border = self.border
            
            # Credit
            credit_cell = ws.cell(row=row, column=4)
            if transaction.credit:
                credit_cell.value = float(transaction.credit)
                credit_cell.font = self.styles['credit']['font']
                credit_cell.fill = self.styles['credit']['fill']
                credit_cell.number_format = '#,##0.00'
            credit_cell.border = self.border
            
            # Balance
            balance_cell = ws.cell(row=row, column=5)
            if transaction.balance:
                balance_cell.value = float(transaction.balance)
                balance_cell.number_format = '#,##0.00'
            balance_cell.border = self.border
        
        # Auto-adjust column widths
        self._adjust_column_widths(ws)
        
        # Add conditional formatting
        self._add_conditional_formatting(ws, len(transactions))
    
    def _create_summary_sheet(
        self, 
        workbook: Workbook, 
        transaction_batches: List[TransactionBatch],
        all_transactions: List[Transaction]
    ):
        """Create summary sheet with statistics."""
        
        ws = workbook.create_sheet(title="Summary", index=0)
        
        # Title
        ws.cell(row=1, column=1, value="Bank Statement Processing Summary").font = Font(size=16, bold=True)
        
        # Processing info
        row = 3
        ws.cell(row=row, column=1, value="Processing Date:").font = Font(bold=True)
        ws.cell(row=row, column=2, value=datetime.now().strftime('%d/%m/%Y %H:%M'))
        
        row += 1
        ws.cell(row=row, column=1, value="Total Files Processed:").font = Font(bold=True)
        ws.cell(row=row, column=2, value=len(transaction_batches))
        
        row += 1
        ws.cell(row=row, column=1, value="Total Transactions:").font = Font(bold=True)
        ws.cell(row=row, column=2, value=len(all_transactions))
        
        # Calculate totals
        total_debits = sum(t.debit for t in all_transactions if t.debit) or Decimal('0')
        total_credits = sum(t.credit for t in all_transactions if t.credit) or Decimal('0')
        
        row += 2
        ws.cell(row=row, column=1, value="Financial Summary").font = Font(size=14, bold=True)
        
        row += 1
        ws.cell(row=row, column=1, value="Total Debits:").font = Font(bold=True)
        debit_cell = ws.cell(row=row, column=2, value=float(total_debits))
        debit_cell.number_format = '#,##0.00'
        debit_cell.font = self.styles['debit']['font']
        
        row += 1
        ws.cell(row=row, column=1, value="Total Credits:").font = Font(bold=True)
        credit_cell = ws.cell(row=row, column=2, value=float(total_credits))
        credit_cell.number_format = '#,##0.00'
        credit_cell.font = self.styles['credit']['font']
        
        row += 1
        ws.cell(row=row, column=1, value="Net Amount:").font = Font(bold=True)
        net_cell = ws.cell(row=row, column=2, value=float(total_credits - total_debits))
        net_cell.number_format = '#,##0.00'
        net_cell.font = Font(bold=True)
        
        # Bank breakdown
        if len(transaction_batches) > 1:
            row += 3
            ws.cell(row=row, column=1, value="Bank Breakdown").font = Font(size=14, bold=True)
            
            bank_stats = {}
            for batch in transaction_batches:
                bank_name = batch.bank_name or 'Unknown'
                if bank_name not in bank_stats:
                    bank_stats[bank_name] = {'transactions': 0, 'debits': Decimal('0'), 'credits': Decimal('0')}
                
                bank_stats[bank_name]['transactions'] += len(batch.transactions)
                for t in batch.transactions:
                    if t.debit:
                        bank_stats[bank_name]['debits'] += t.debit
                    if t.credit:
                        bank_stats[bank_name]['credits'] += t.credit
            
            row += 1
            headers = ['Bank', 'Transactions', 'Debits', 'Credits', 'Net']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=row, column=col, value=header)
                cell.font = Font(bold=True)
                cell.border = self.border
            
            for bank_name, stats in bank_stats.items():
                row += 1
                ws.cell(row=row, column=1, value=bank_name).border = self.border
                ws.cell(row=row, column=2, value=stats['transactions']).border = self.border
                
                debit_cell = ws.cell(row=row, column=3, value=float(stats['debits']))
                debit_cell.number_format = '#,##0.00'
                debit_cell.border = self.border
                
                credit_cell = ws.cell(row=row, column=4, value=float(stats['credits']))
                credit_cell.number_format = '#,##0.00'
                credit_cell.border = self.border
                
                net_cell = ws.cell(row=row, column=5, value=float(stats['credits'] - stats['debits']))
                net_cell.number_format = '#,##0.00'
                net_cell.border = self.border
        
        # Auto-adjust column widths
        self._adjust_column_widths(ws)

    def _adjust_column_widths(self, worksheet):
        """Auto-adjust column widths based on content."""
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter

            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass

            adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
            worksheet.column_dimensions[column_letter].width = adjusted_width

    def _add_conditional_formatting(self, worksheet, num_transactions: int):
        """Add conditional formatting to highlight debits and credits."""
        if num_transactions == 0:
            return

        # Highlight debits in red
        debit_rule = CellIsRule(
            operator='greaterThan',
            formula=['0'],
            fill=self.styles['debit']['fill'],
            font=self.styles['debit']['font']
        )
        worksheet.conditional_formatting.add(f'C2:C{num_transactions + 1}', debit_rule)

        # Highlight credits in green
        credit_rule = CellIsRule(
            operator='greaterThan',
            formula=['0'],
            fill=self.styles['credit']['fill'],
            font=self.styles['credit']['font']
        )
        worksheet.conditional_formatting.add(f'D2:D{num_transactions + 1}', credit_rule)

    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for file system compatibility."""
        import re
        # Remove or replace invalid characters
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
        sanitized = re.sub(r'\s+', '_', sanitized)  # Replace spaces with underscores
        return sanitized.strip('_')

    def _sanitize_sheet_name(self, sheet_name: str) -> str:
        """Sanitize sheet name for Excel compatibility."""
        import re
        # Excel sheet name restrictions
        sanitized = re.sub(r'[\\/*?:\[\]]', '_', sheet_name)
        return sanitized[:31]  # Excel limit is 31 characters

    def export_to_csv(self, transactions: List[Transaction], output_path: Path) -> Dict[str, Any]:
        """Export transactions to CSV format."""
        try:
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # Convert transactions to DataFrame
            data = []
            for transaction in transactions:
                data.append({
                    'Date': transaction.date.strftime('%d/%m/%Y'),
                    'Description': transaction.description,
                    'Debit': float(transaction.debit) if transaction.debit else None,
                    'Credit': float(transaction.credit) if transaction.credit else None,
                    'Balance': float(transaction.balance) if transaction.balance else None,
                    'Bank': transaction.bank_name or '',
                    'Reference': transaction.reference or '',
                    'Category': transaction.category or ''
                })

            df = pd.DataFrame(data)
            df.to_csv(output_path, index=False, encoding='utf-8')

            self.logger.info(f"Exported {len(transactions)} transactions to CSV: {output_path}")

            return {
                'success': True,
                'file_path': str(output_path),
                'error': None
            }

        except Exception as e:
            self.logger.error(f"Error exporting to CSV: {e}")
            return {
                'success': False,
                'file_path': None,
                'error': str(e)
            }

    def export_to_json(self, transaction_batches: List[TransactionBatch], output_path: Path) -> Dict[str, Any]:
        """Export transaction batches to JSON format."""
        try:
            import json

            output_path.parent.mkdir(parents=True, exist_ok=True)

            # Convert batches to serializable format
            data = {
                'export_date': datetime.now().isoformat(),
                'total_batches': len(transaction_batches),
                'batches': []
            }

            for batch in transaction_batches:
                batch_data = {
                    'bank_name': batch.bank_name,
                    'account_number': batch.account_number,
                    'statement_period': batch.statement_period,
                    'processed_at': batch.processed_at.isoformat() if batch.processed_at else None,
                    'ai_provider': batch.ai_provider,
                    'confidence_score': batch.confidence_score,
                    'transactions': [t.to_dict() for t in batch.transactions]
                }
                data['batches'].append(batch_data)

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            total_transactions = sum(len(batch.transactions) for batch in transaction_batches)
            self.logger.info(f"Exported {total_transactions} transactions to JSON: {output_path}")

            return {
                'success': True,
                'file_path': str(output_path),
                'error': None
            }

        except Exception as e:
            self.logger.error(f"Error exporting to JSON: {e}")
            return {
                'success': False,
                'file_path': None,
                'error': str(e)
            }


# Global Excel generator instance
excel_generator = ExcelGenerator()


def generate_excel_workbook(
    transaction_batches: List[TransactionBatch],
    output_path: Path,
    separate_banks: bool = None,
    monthly_sheets: bool = None
) -> Dict[str, Any]:
    """Convenience function to generate Excel workbook."""
    return excel_generator.generate_workbook(
        transaction_batches, output_path, separate_banks, monthly_sheets
    )


def export_to_csv(transactions: List[Transaction], output_path: Path) -> Dict[str, Any]:
    """Convenience function to export to CSV."""
    return excel_generator.export_to_csv(transactions, output_path)


def export_to_json(transaction_batches: List[TransactionBatch], output_path: Path) -> Dict[str, Any]:
    """Convenience function to export to JSON."""
    return excel_generator.export_to_json(transaction_batches, output_path)

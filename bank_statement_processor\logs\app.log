2025-07-13 21:39:32 - __main__ - [32m<PERSON>F<PERSON>[0m - start_processing:243 - Processing session started
2025-07-13 21:39:32 - processors.statement_reader - [32mINFO[0m - log_execution_time:220 - Operation 'read_statement_.pdf' completed successfully in 0.04s
2025-07-13 21:39:32 - processors.ai_processor - [32mINFO[0m - process_with_fallback:429 - Trying fallback provider: openai
2025-07-13 21:39:33 - processors.ai_processor - [33mWARNING[0m - _make_ai_request:184 - AI request attempt 1 failed: OpenAI API error: 401 - {
    "error": {
        "message": "Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.",
        "type": "invalid_request_error",
        "param": null,
        "code": "invalid_api_key"
    }
}

2025-07-13 21:39:36 - processors.ai_processor - [33mWARNING[0m - _make_ai_request:184 - AI request attempt 2 failed: OpenAI API error: 401 - {
    "error": {
        "message": "Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.",
        "type": "invalid_request_error",
        "param": null,
        "code": "invalid_api_key"
    }
}

2025-07-13 21:39:39 - processors.ai_processor - [33mWARNING[0m - _make_ai_request:184 - AI request attempt 3 failed: OpenAI API error: 401 - {
    "error": {
        "message": "Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.",
        "type": "invalid_request_error",
        "param": null,
        "code": "invalid_api_key"
    }
}

2025-07-13 21:39:39 - processors.ai_processor - [31mERROR[0m - identify_bank:119 - Bank identification failed: OpenAI API error: 401 - {
    "error": {
        "message": "Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.",
        "type": "invalid_request_error",
        "param": null,
        "code": "invalid_api_key"
    }
}

2025-07-13 21:39:39 - processors.ai_processor - [32mINFO[0m - log_execution_time:220 - Operation 'bank_identification_openai' completed successfully in 6.37s
2025-07-13 21:39:39 - processors.ai_processor - [32mINFO[0m - process_with_fallback:429 - Trying fallback provider: openai
2025-07-13 21:39:40 - processors.ai_processor - [33mWARNING[0m - _make_ai_request:184 - AI request attempt 1 failed: OpenAI API error: 401 - {
    "error": {
        "message": "Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.",
        "type": "invalid_request_error",
        "param": null,
        "code": "invalid_api_key"
    }
}

2025-07-13 21:39:41 - processors.ai_processor - [33mWARNING[0m - _make_ai_request:184 - AI request attempt 2 failed: OpenAI API error: 401 - {
    "error": {
        "message": "Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.",
        "type": "invalid_request_error",
        "param": null,
        "code": "invalid_api_key"
    }
}

2025-07-13 21:39:44 - processors.ai_processor - [33mWARNING[0m - _make_ai_request:184 - AI request attempt 3 failed: OpenAI API error: 401 - {
    "error": {
        "message": "Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.",
        "type": "invalid_request_error",
        "param": null,
        "code": "invalid_api_key"
    }
}

2025-07-13 21:39:44 - processors.ai_processor - [31mERROR[0m - extract_transactions:160 - Transaction extraction failed: OpenAI API error: 401 - {
    "error": {
        "message": "Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.",
        "type": "invalid_request_error",
        "param": null,
        "code": "invalid_api_key"
    }
}

2025-07-13 21:39:44 - processors.ai_processor - [32mINFO[0m - log_execution_time:220 - Operation 'transaction_extraction_openai' completed successfully in 5.74s
2025-07-13 21:39:44 - __main__ - [32mINFO[0m - log_file_success:267 - Successfully processed: statements\ADCBStmt_NAEEM AHMAD HANFI_162908790_15912 june2025.PDF (0 transactions)
2025-07-13 21:39:44 - processors.excel_generator - [31mERROR[0m - _generate_separate_bank_workbooks:119 - Failed to create workbook for Unknown: No transactions to write
2025-07-13 21:39:44 - processors.excel_generator - [32mINFO[0m - log_execution_time:220 - Operation 'generate_excel_workbook' completed successfully in 0.00s
2025-07-13 21:39:44 - __main__ - [32mINFO[0m - end_processing:253 - Processing session completed

"""
Statement reader for processing multiple file formats.
"""

import io
import csv
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import pandas as pd
from PIL import Image
import pytesseract
import PyPDF2
import chardet

from ..config.settings import get_settings
from ..utils.logger import get_logger, log_execution_time
from ..utils.file_handler import FileHandler


class StatementReader:
    """Reader for various statement file formats."""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger(__name__)
        self.file_handler = FileHandler(self.settings.max_file_size_mb)
        
        # OCR configuration
        self.ocr_config = {
            'lang': self.settings.ocr_language,
            'config': '--psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,/-:() '
        }
    
    def read_statement(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """Read statement from file and return extracted text."""
        file_path = Path(file_path)
        
        # Validate file
        validation_result = self.file_handler.validate_file(file_path)
        if not validation_result['valid']:
            return {
                'success': False,
                'text': '',
                'file_info': validation_result,
                'error': '; '.join(validation_result['errors'])
            }
        
        file_type = validation_result['file_type']
        
        with log_execution_time(self.logger, f"read_statement_{file_type}"):
            try:
                # Route to appropriate reader based on file type
                if file_type == '.pdf':
                    text = self._read_pdf(file_path)
                elif file_type == '.csv':
                    text = self._read_csv(file_path, validation_result.get('encoding', 'utf-8'))
                elif file_type in ['.xlsx', '.xls']:
                    text = self._read_excel(file_path)
                elif file_type == '.txt':
                    text = self._read_text(file_path, validation_result.get('encoding', 'utf-8'))
                elif file_type in ['.jpg', '.jpeg', '.png']:
                    text = self._read_image(file_path)
                else:
                    raise ValueError(f"Unsupported file type: {file_type}")
                
                return {
                    'success': True,
                    'text': text,
                    'file_info': validation_result,
                    'error': None
                }
            
            except Exception as e:
                self.logger.error(f"Error reading file {file_path}: {e}")
                return {
                    'success': False,
                    'text': '',
                    'file_info': validation_result,
                    'error': str(e)
                }
    
    def _read_pdf(self, file_path: Path) -> str:
        """Read text from PDF file."""
        text_content = []
        
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            text_content.append(f"--- Page {page_num + 1} ---\n{page_text}")
                    except Exception as e:
                        self.logger.warning(f"Error reading page {page_num + 1}: {e}")
                        continue
            
            if not text_content:
                self.logger.warning(f"No text extracted from PDF: {file_path}")
                return ""
            
            return "\n\n".join(text_content)
        
        except Exception as e:
            self.logger.error(f"Error reading PDF {file_path}: {e}")
            raise
    
    def _read_csv(self, file_path: Path, encoding: str = 'utf-8') -> str:
        """Read text from CSV file."""
        try:
            # Try to read with pandas first for better handling
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                return self._dataframe_to_text(df)
            except Exception:
                # Fallback to manual CSV reading
                with open(file_path, 'r', encoding=encoding, newline='') as file:
                    # Detect delimiter
                    sample = file.read(1024)
                    file.seek(0)
                    
                    sniffer = csv.Sniffer()
                    delimiter = sniffer.sniff(sample).delimiter
                    
                    reader = csv.reader(file, delimiter=delimiter)
                    rows = []
                    
                    for row_num, row in enumerate(reader):
                        if row_num == 0:
                            # Header row
                            rows.append("Headers: " + " | ".join(row))
                        else:
                            rows.append(" | ".join(row))
                    
                    return "\n".join(rows)
        
        except Exception as e:
            self.logger.error(f"Error reading CSV {file_path}: {e}")
            raise
    
    def _read_excel(self, file_path: Path) -> str:
        """Read text from Excel file."""
        try:
            # Read all sheets
            excel_file = pd.ExcelFile(file_path)
            text_content = []
            
            for sheet_name in excel_file.sheet_names:
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    if not df.empty:
                        sheet_text = f"--- Sheet: {sheet_name} ---\n"
                        sheet_text += self._dataframe_to_text(df)
                        text_content.append(sheet_text)
                except Exception as e:
                    self.logger.warning(f"Error reading sheet {sheet_name}: {e}")
                    continue
            
            return "\n\n".join(text_content)
        
        except Exception as e:
            self.logger.error(f"Error reading Excel {file_path}: {e}")
            raise
    
    def _read_text(self, file_path: Path, encoding: str = 'utf-8') -> str:
        """Read text from plain text file."""
        try:
            return self.file_handler.read_text_file(file_path, encoding)
        except Exception as e:
            self.logger.error(f"Error reading text file {file_path}: {e}")
            raise
    
    def _read_image(self, file_path: Path) -> str:
        """Read text from image using OCR."""
        if not self.settings.enable_ocr:
            raise ValueError("OCR is disabled in settings")
        
        try:
            # Open and preprocess image
            image = Image.open(file_path)
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Perform OCR
            text = pytesseract.image_to_string(
                image,
                lang=self.ocr_config['lang'],
                config=self.ocr_config['config']
            )
            
            if not text.strip():
                self.logger.warning(f"No text extracted from image: {file_path}")
                return ""
            
            return text
        
        except Exception as e:
            self.logger.error(f"Error reading image {file_path}: {e}")
            raise
    
    def _dataframe_to_text(self, df: pd.DataFrame) -> str:
        """Convert pandas DataFrame to readable text format."""
        if df.empty:
            return "No data found"
        
        text_lines = []
        
        # Add headers
        headers = " | ".join(str(col) for col in df.columns)
        text_lines.append(f"Headers: {headers}")
        
        # Add separator
        text_lines.append("-" * len(headers))
        
        # Add data rows
        for index, row in df.iterrows():
            row_text = " | ".join(str(value) if pd.notna(value) else "" for value in row)
            text_lines.append(row_text)
        
        return "\n".join(text_lines)
    
    def batch_read_statements(self, file_paths: List[Union[str, Path]]) -> Dict[str, Dict[str, Any]]:
        """Read multiple statement files."""
        results = {}
        
        for file_path in file_paths:
            file_path = Path(file_path)
            self.logger.info(f"Reading file: {file_path}")
            
            try:
                result = self.read_statement(file_path)
                results[str(file_path)] = result
                
                if result['success']:
                    self.logger.info(f"Successfully read {file_path} ({len(result['text'])} characters)")
                else:
                    self.logger.error(f"Failed to read {file_path}: {result['error']}")
            
            except Exception as e:
                self.logger.error(f"Unexpected error reading {file_path}: {e}")
                results[str(file_path)] = {
                    'success': False,
                    'text': '',
                    'file_info': {},
                    'error': str(e)
                }
        
        return results
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported file formats."""
        return self.settings.supported_formats_list
    
    def preprocess_text(self, text: str) -> str:
        """Preprocess extracted text for better AI processing."""
        if not text:
            return ""
        
        # Remove excessive whitespace
        import re
        text = re.sub(r'\n\s*\n', '\n\n', text)  # Multiple newlines to double newline
        text = re.sub(r' +', ' ', text)  # Multiple spaces to single space
        
        # Remove page markers and common noise
        text = re.sub(r'--- Page \d+ ---\n?', '', text)
        text = re.sub(r'Page \d+ of \d+', '', text)
        
        return text.strip()


# Global statement reader instance
statement_reader = StatementReader()


def read_statement(file_path: Union[str, Path]) -> Dict[str, Any]:
    """Convenience function to read a statement file."""
    return statement_reader.read_statement(file_path)


def batch_read_statements(file_paths: List[Union[str, Path]]) -> Dict[str, Dict[str, Any]]:
    """Convenience function to read multiple statement files."""
    return statement_reader.batch_read_statements(file_paths)

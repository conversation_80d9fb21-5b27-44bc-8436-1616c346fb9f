"""
LLM provider configurations and client management.
"""

from typing import Dict, Any, Optional, Type
from dataclasses import dataclass
from abc import ABC, abstractmethod
import asyncio
import aiohttp
import json


@dataclass
class LLMConfig:
    """Configuration for an LLM provider."""
    name: str
    api_key: str
    model: str
    base_url: Optional[str] = None
    temperature: float = 0.1
    max_tokens: int = 4000
    timeout: int = 30
    rate_limit: int = 60  # requests per minute
    cost_per_1k_tokens: float = 0.0  # for cost tracking


class LLMProvider(ABC):
    """Abstract base class for LLM providers."""
    
    def __init__(self, config: LLMConfig):
        self.config = config
        self.name = config.name
    
    @abstractmethod
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """Generate a response from the LLM."""
        pass
    
    @abstractmethod
    def validate_config(self) -> bool:
        """Validate the provider configuration."""
        pass
    
    @abstractmethod
    def estimate_cost(self, prompt: str, response: str = "") -> float:
        """Estimate the cost of a request."""
        pass


class OpenAIProvider(LLMProvider):
    """OpenAI provider implementation."""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.base_url = "https://api.openai.com/v1"
    
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response using OpenAI API."""
        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.config.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": kwargs.get("temperature", self.config.temperature),
            "max_tokens": kwargs.get("max_tokens", self.config.max_tokens)
        }
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.config.timeout)) as session:
            async with session.post(f"{self.base_url}/chat/completions", headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    return result["choices"][0]["message"]["content"]
                else:
                    error_text = await response.text()
                    raise Exception(f"OpenAI API error: {response.status} - {error_text}")
    
    def validate_config(self) -> bool:
        """Validate OpenAI configuration."""
        return bool(self.config.api_key and self.config.model)
    
    def estimate_cost(self, prompt: str, response: str = "") -> float:
        """Estimate cost based on token count."""
        # Rough estimation: 1 token ≈ 4 characters
        prompt_tokens = len(prompt) // 4
        response_tokens = len(response) // 4
        total_tokens = prompt_tokens + response_tokens
        return (total_tokens / 1000) * self.config.cost_per_1k_tokens


class AnthropicProvider(LLMProvider):
    """Anthropic Claude provider implementation."""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.base_url = "https://api.anthropic.com/v1"
    
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response using Anthropic API."""
        headers = {
            "x-api-key": self.config.api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }
        
        data = {
            "model": self.config.model,
            "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
            "messages": [{"role": "user", "content": prompt}],
            "temperature": kwargs.get("temperature", self.config.temperature)
        }
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.config.timeout)) as session:
            async with session.post(f"{self.base_url}/messages", headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    return result["content"][0]["text"]
                else:
                    error_text = await response.text()
                    raise Exception(f"Anthropic API error: {response.status} - {error_text}")
    
    def validate_config(self) -> bool:
        """Validate Anthropic configuration."""
        return bool(self.config.api_key and self.config.model)
    
    def estimate_cost(self, prompt: str, response: str = "") -> float:
        """Estimate cost based on token count."""
        prompt_tokens = len(prompt) // 4
        response_tokens = len(response) // 4
        total_tokens = prompt_tokens + response_tokens
        return (total_tokens / 1000) * self.config.cost_per_1k_tokens


class GoogleProvider(LLMProvider):
    """Google Gemini provider implementation."""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
    
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response using Google Gemini API."""
        headers = {"Content-Type": "application/json"}
        
        data = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {
                "temperature": kwargs.get("temperature", self.config.temperature),
                "maxOutputTokens": kwargs.get("max_tokens", self.config.max_tokens)
            }
        }
        
        url = f"{self.base_url}/models/{self.config.model}:generateContent?key={self.config.api_key}"
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.config.timeout)) as session:
            async with session.post(url, headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    return result["candidates"][0]["content"]["parts"][0]["text"]
                else:
                    error_text = await response.text()
                    raise Exception(f"Google API error: {response.status} - {error_text}")
    
    def validate_config(self) -> bool:
        """Validate Google configuration."""
        return bool(self.config.api_key and self.config.model)
    
    def estimate_cost(self, prompt: str, response: str = "") -> float:
        """Estimate cost based on token count."""
        prompt_tokens = len(prompt) // 4
        response_tokens = len(response) // 4
        total_tokens = prompt_tokens + response_tokens
        return (total_tokens / 1000) * self.config.cost_per_1k_tokens


class CustomProvider(LLMProvider):
    """Custom LLM provider for any OpenAI-compatible API."""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        if not config.base_url:
            raise ValueError("Custom provider requires base_url")
    
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """Generate response using custom API."""
        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.config.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": kwargs.get("temperature", self.config.temperature),
            "max_tokens": kwargs.get("max_tokens", self.config.max_tokens)
        }
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.config.timeout)) as session:
            async with session.post(f"{self.config.base_url}/chat/completions", headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    return result["choices"][0]["message"]["content"]
                else:
                    error_text = await response.text()
                    raise Exception(f"Custom API error: {response.status} - {error_text}")
    
    def validate_config(self) -> bool:
        """Validate custom provider configuration."""
        return bool(self.config.api_key and self.config.model and self.config.base_url)
    
    def estimate_cost(self, prompt: str, response: str = "") -> float:
        """Estimate cost based on token count."""
        prompt_tokens = len(prompt) // 4
        response_tokens = len(response) // 4
        total_tokens = prompt_tokens + response_tokens
        return (total_tokens / 1000) * self.config.cost_per_1k_tokens


# Provider registry
PROVIDER_CLASSES: Dict[str, Type[LLMProvider]] = {
    "openai": OpenAIProvider,
    "anthropic": AnthropicProvider,
    "google": GoogleProvider,
    "mistral": CustomProvider,  # Mistral uses OpenAI-compatible API
    "deepseek": CustomProvider,  # DeepSeek uses OpenAI-compatible API
    "openrouter": CustomProvider,  # OpenRouter uses OpenAI-compatible API
    "custom": CustomProvider,
}

# Default cost per 1k tokens (USD) - approximate values
DEFAULT_COSTS = {
    "openai": 0.03,
    "anthropic": 0.015,
    "google": 0.001,
    "mistral": 0.002,
    "deepseek": 0.0014,
    "openrouter": 0.02,
    "custom": 0.0,
}


def create_llm_config(provider: str, api_key: str, model: str, **kwargs) -> LLMConfig:
    """Create an LLM configuration for a provider."""
    base_urls = {
        "mistral": "https://api.mistral.ai/v1",
        "deepseek": "https://api.deepseek.com/v1",
        "openrouter": "https://openrouter.ai/api/v1",
    }
    
    return LLMConfig(
        name=provider,
        api_key=api_key,
        model=model,
        base_url=kwargs.get("base_url", base_urls.get(provider)),
        temperature=kwargs.get("temperature", 0.1),
        max_tokens=kwargs.get("max_tokens", 4000),
        timeout=kwargs.get("timeout", 30),
        rate_limit=kwargs.get("rate_limit", 60),
        cost_per_1k_tokens=kwargs.get("cost_per_1k_tokens", DEFAULT_COSTS.get(provider, 0.0))
    )


def create_provider(provider_name: str, config: LLMConfig) -> LLMProvider:
    """Create an LLM provider instance."""
    provider_class = PROVIDER_CLASSES.get(provider_name)
    if not provider_class:
        raise ValueError(f"Unknown provider: {provider_name}")
    
    return provider_class(config)

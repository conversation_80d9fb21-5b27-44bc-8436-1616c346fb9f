"""
Comprehensive error handling utilities for bank statement processor.
"""

import traceback
import sys
from typing import Dict, Any, Optional, Callable, Type
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
import functools

from .logger import get_logger


class ErrorSeverity(str, Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(str, Enum):
    """Error categories."""
    FILE_PROCESSING = "file_processing"
    AI_API = "ai_api"
    DATA_VALIDATION = "data_validation"
    CONFIGURATION = "configuration"
    NETWORK = "network"
    SYSTEM = "system"
    USER_INPUT = "user_input"


@dataclass
class ErrorInfo:
    """Structured error information."""
    error_type: str
    message: str
    category: ErrorCategory
    severity: ErrorSeverity
    timestamp: datetime
    context: Dict[str, Any]
    traceback_info: Optional[str] = None
    suggested_action: Optional[str] = None
    error_code: Optional[str] = None


class BankStatementProcessorError(Exception):
    """Base exception for bank statement processor."""
    
    def __init__(
        self, 
        message: str, 
        category: ErrorCategory = ErrorCategory.SYSTEM,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[Dict[str, Any]] = None,
        suggested_action: Optional[str] = None,
        error_code: Optional[str] = None
    ):
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.context = context or {}
        self.suggested_action = suggested_action
        self.error_code = error_code
        self.timestamp = datetime.now()


class FileProcessingError(BankStatementProcessorError):
    """Error during file processing."""
    
    def __init__(self, message: str, file_path: str = None, **kwargs):
        super().__init__(
            message, 
            category=ErrorCategory.FILE_PROCESSING,
            **kwargs
        )
        if file_path:
            self.context['file_path'] = file_path


class AIAPIError(BankStatementProcessorError):
    """Error during AI API calls."""
    
    def __init__(self, message: str, provider: str = None, model: str = None, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.AI_API,
            **kwargs
        )
        if provider:
            self.context['provider'] = provider
        if model:
            self.context['model'] = model


class DataValidationError(BankStatementProcessorError):
    """Error during data validation."""
    
    def __init__(self, message: str, field: str = None, value: Any = None, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.DATA_VALIDATION,
            **kwargs
        )
        if field:
            self.context['field'] = field
        if value is not None:
            self.context['value'] = str(value)


class ConfigurationError(BankStatementProcessorError):
    """Error in configuration."""
    
    def __init__(self, message: str, config_key: str = None, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.CONFIGURATION,
            severity=ErrorSeverity.HIGH,
            **kwargs
        )
        if config_key:
            self.context['config_key'] = config_key


class ErrorHandler:
    """Centralized error handler."""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.error_counts = {}
        self.error_history = []
        
        # Error recovery strategies
        self.recovery_strategies = {
            ErrorCategory.AI_API: self._recover_ai_api_error,
            ErrorCategory.FILE_PROCESSING: self._recover_file_processing_error,
            ErrorCategory.NETWORK: self._recover_network_error,
        }
    
    def handle_error(
        self, 
        error: Exception, 
        context: Optional[Dict[str, Any]] = None,
        reraise: bool = True
    ) -> ErrorInfo:
        """Handle an error with comprehensive logging and recovery."""
        
        # Create error info
        if isinstance(error, BankStatementProcessorError):
            error_info = ErrorInfo(
                error_type=type(error).__name__,
                message=error.message,
                category=error.category,
                severity=error.severity,
                timestamp=error.timestamp,
                context={**error.context, **(context or {})},
                suggested_action=error.suggested_action,
                error_code=error.error_code,
                traceback_info=traceback.format_exc()
            )
        else:
            error_info = ErrorInfo(
                error_type=type(error).__name__,
                message=str(error),
                category=ErrorCategory.SYSTEM,
                severity=ErrorSeverity.MEDIUM,
                timestamp=datetime.now(),
                context=context or {},
                traceback_info=traceback.format_exc()
            )
        
        # Log error
        self._log_error(error_info)
        
        # Update error counts
        self._update_error_counts(error_info)
        
        # Add to history
        self.error_history.append(error_info)
        
        # Attempt recovery
        recovery_result = self._attempt_recovery(error_info)
        if recovery_result:
            self.logger.info(f"Error recovery successful: {recovery_result}")
            return error_info
        
        # Reraise if requested
        if reraise:
            raise error
        
        return error_info
    
    def _log_error(self, error_info: ErrorInfo):
        """Log error with appropriate level."""
        log_message = f"{error_info.error_type}: {error_info.message}"
        
        extra_data = {
            'error_category': error_info.category,
            'error_severity': error_info.severity,
            'error_context': error_info.context,
            'error_code': error_info.error_code,
            'suggested_action': error_info.suggested_action
        }
        
        if error_info.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message, extra=extra_data, exc_info=True)
        elif error_info.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message, extra=extra_data, exc_info=True)
        elif error_info.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message, extra=extra_data)
        else:
            self.logger.info(log_message, extra=extra_data)
    
    def _update_error_counts(self, error_info: ErrorInfo):
        """Update error count statistics."""
        key = f"{error_info.category}:{error_info.error_type}"
        self.error_counts[key] = self.error_counts.get(key, 0) + 1
    
    def _attempt_recovery(self, error_info: ErrorInfo) -> Optional[str]:
        """Attempt to recover from error."""
        recovery_strategy = self.recovery_strategies.get(error_info.category)
        if recovery_strategy:
            try:
                return recovery_strategy(error_info)
            except Exception as e:
                self.logger.error(f"Error recovery failed: {e}")
        
        return None
    
    def _recover_ai_api_error(self, error_info: ErrorInfo) -> Optional[str]:
        """Attempt to recover from AI API errors."""
        # This could implement fallback to different providers
        return "Fallback to alternative AI provider"
    
    def _recover_file_processing_error(self, error_info: ErrorInfo) -> Optional[str]:
        """Attempt to recover from file processing errors."""
        # This could implement alternative file reading methods
        return "Alternative file processing method attempted"
    
    def _recover_network_error(self, error_info: ErrorInfo) -> Optional[str]:
        """Attempt to recover from network errors."""
        # This could implement retry with exponential backoff
        return "Network retry attempted"
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of errors encountered."""
        return {
            'total_errors': len(self.error_history),
            'error_counts': self.error_counts.copy(),
            'recent_errors': [
                {
                    'type': e.error_type,
                    'message': e.message,
                    'category': e.category,
                    'severity': e.severity,
                    'timestamp': e.timestamp.isoformat()
                }
                for e in self.error_history[-10:]  # Last 10 errors
            ]
        }
    
    def clear_error_history(self):
        """Clear error history and counts."""
        self.error_history.clear()
        self.error_counts.clear()


def error_handler_decorator(
    category: ErrorCategory = ErrorCategory.SYSTEM,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    reraise: bool = True
):
    """Decorator for automatic error handling."""
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = {
                    'function': func.__name__,
                    'args': str(args)[:200],  # Truncate long args
                    'kwargs': str(kwargs)[:200]
                }
                
                if not isinstance(e, BankStatementProcessorError):
                    e = BankStatementProcessorError(
                        str(e),
                        category=category,
                        severity=severity,
                        context=context
                    )
                
                error_handler.handle_error(e, context, reraise)
        
        return wrapper
    return decorator


def async_error_handler_decorator(
    category: ErrorCategory = ErrorCategory.SYSTEM,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    reraise: bool = True
):
    """Async decorator for automatic error handling."""
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                context = {
                    'function': func.__name__,
                    'args': str(args)[:200],
                    'kwargs': str(kwargs)[:200]
                }
                
                if not isinstance(e, BankStatementProcessorError):
                    e = BankStatementProcessorError(
                        str(e),
                        category=category,
                        severity=severity,
                        context=context
                    )
                
                error_handler.handle_error(e, context, reraise)
        
        return wrapper
    return decorator


# Global error handler instance
error_handler = ErrorHandler()


def handle_error(error: Exception, context: Optional[Dict[str, Any]] = None, reraise: bool = True) -> ErrorInfo:
    """Convenience function to handle errors."""
    return error_handler.handle_error(error, context, reraise)


def get_error_summary() -> Dict[str, Any]:
    """Convenience function to get error summary."""
    return error_handler.get_error_summary()

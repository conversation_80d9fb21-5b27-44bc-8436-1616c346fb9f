AI-Powered Bank Statement Processing Application

Project Overview
Create a comprehensive Python application that processes bank statements using AI APIs to extract transaction data and generate organized Excel reports.

Core Requirements

1. Application Structure
```
bank_statement_processor/
├── main.py                 Main application entry point
├── config/
│   ├── __init__.py
│   ├── settings.py        Configuration management
│   └── llm_config.py      LLM provider configurations
├── processors/
│   ├── __init__.py
│   ├── statement_reader.py    File reading and parsing
│   ├── ai_processor.py       AI integration
│   └── excel_generator.py    Excel file creation
├── utils/
│   ├── __init__.py
│   ├── file_handler.py      File operations
│   ├── date_parser.py       Date parsing utilities
│   └── logger.py           Logging system
├── models/
│   ├── __init__.py
│   ├── transaction.py      Transaction data model
│   └── bank_config.py      Bank-specific configurations
├── statements/             Input folder for statements
├── processed/             Processed statements archive
├── output/                Generated Excel files
├── logs/                  Application logs
├── requirements.txt
├── .env.example
└── README.md
```

2. Supported LLM Providers
Implement support for multiple AI providers with a unified interface:

- OpenAI (GPT-4, GPT-3.5)
- <PERSON> (Anthropic)
- Gemini (Google)
- Mistral
- DeepSeek
- OpenRouter (multiple models)
- Custom LLM (configurable endpoint)

3. Core Features

A. Configuration Management
- Support for multiple API keys and providers
- Environment variable configuration
- Settings validation and error handling
- Provider-specific parameters (temperature, max_tokens, etc.)

B. File Processing
- Support multiple file formats: PDF, CSV, Excel, TXT, images (JPG, PNG)
- Automatic file type detection
- OCR capabilities for image-based statements
- Multi-language support for statement processing

C. AI Integration
- Intelligent transaction extraction
- Bank format recognition
- Data validation and cleaning
- Error correction and formatting

D. Excel Generation
- Separate workbooks for each bank
- Monthly sheets within each workbook
- Standardized headers: Date, Transaction Detail, Debit, Credit, Balance
- Proper formatting and styling

4. Implementation Requirements

A. Main Application (main.py)
```python
Create a CLI interface with the following options:
1. Process all statements
2. Process specific bank statements
3. Configure AI providers
4. View processing history
5. Generate reports

Include progress bars and status updates
Implement graceful error handling
Add logging for all operations
```

B. Configuration System (config/settings.py)
```python
Support for multiple configuration methods:
- Environment variables
- Config file (JSON/YAML)
- Command line arguments

Required configurations:
- API keys for each provider
- Default AI provider
- Processing parameters
- File paths
- Logging levels
```

C. AI Processor (processors/ai_processor.py)
```python
Unified AI interface supporting all providers
Implement retry mechanisms and fallback providers
Token usage tracking and cost estimation
Response validation and error handling

AI Prompts for:
1. Bank identification
2. Transaction extraction
3. Date normalization
4. Amount parsing
5. Description cleaning
```

D. Statement Reader (processors/statement_reader.py)
```python
Support for multiple file formats
Implement OCR for image files
Text extraction from PDFs
CSV/Excel parsing with encoding detection
Multi-language text processing
```

E. Excel Generator (processors/excel_generator.py)
```python
Create organized Excel workbooks
Monthly sheet separation
Proper date formatting
Currency formatting for amounts
Conditional formatting for debits/credits
Summary sheets with totals
```

5. Error Handling Strategy

A. File Processing Errors
- Invalid file formats
- Corrupted files
- Missing files
- Permission issues

B. AI API Errors
- API key validation
- Rate limit handling
- Network timeouts
- Response parsing errors
- Provider-specific error codes

C. Data Processing Errors
- Invalid date formats
- Unparseable amounts
- Missing transaction data
- Duplicate detection

6. User Experience Features

A. Interactive Setup
- First-run configuration wizard
- API key validation
- Provider testing
- Sample processing

B. Progress Tracking
- Real-time processing status
- File progress indicators
- Batch processing updates
- Error summaries

C. Reporting
- Processing statistics
- Error reports
- API usage tracking
- Performance metrics

7. Advanced Features

A. Bank-Specific Processing
- Configurable bank profiles
- Format recognition patterns
- Custom parsing rules
- Validation logic

B. Data Validation
- Transaction consistency checks
- Balance validation
- Duplicate detection
- Data quality scoring

C. Export Options
- Multiple Excel formats
- CSV export capability
- JSON data export
- Report templates

8. Technical Requirements

A. Dependencies
```python
Core libraries
pandas>=1.5.0
openpyxl>=3.0.0
python-dotenv>=0.19.0
click>=8.0.0
tqdm>=4.64.0

AI providers
openai>=1.0.0
anthropic>=0.3.0
google-generativeai>=0.3.0
mistralai>=0.0.8

File processing
PyPDF2>=3.0.0
pytesseract>=0.3.10
Pillow>=9.0.0
chardet>=5.0.0

Utilities
colorama>=0.4.6
rich>=13.0.0
pydantic>=1.10.0
```

B. Performance Considerations
- Concurrent file processing
- Memory-efficient PDF processing
- Caching for repeated operations
- Batch AI requests optimization

9. Security Features
- Secure API key storage
- Input validation
- File type verification
- Path traversal protection

10. Testing Requirements
- Unit tests for all components
- Integration tests for AI providers
- File processing tests
- Excel generation validation

Implementation Instructions

1. Start with the basic structure: Create the folder structure and main entry point
2. Implement configuration system: Build robust settings management
3. Create AI provider interface: Unified API for all LLM providers
4. Build file processors: Handle different statement formats
5. Develop Excel generator: Create organized output files
6. Add error handling: Comprehensive error management
7. Implement CLI interface: User-friendly command-line interface
8. Add logging and monitoring: Track all operations
9. Create documentation: Comprehensive user guide
10. Add tests: Ensure reliability and maintainability

AI Prompt Templates

Transaction Extraction Prompt
```
You are a bank statement processor. Extract transaction details from the following bank statement text. 

Requirements:
1. Extract only transaction data (ignore headers, footers, summaries)
2. For each transaction, identify: Date, Description, Debit Amount, Credit Amount
3. Normalize dates to DD/MM/YYYY format
4. Extract amounts as numbers without currency symbols
5. Clean and standardize transaction descriptions
6. Ignore non-transaction lines (balances, fees explanations, etc.)

Output format: JSON array with objects containing: date, description, debit, credit

Bank Statement Text:
{statement_text}
```

Bank Identification Prompt
```
Identify the bank from this statement text and provide the bank name and country.
Also indicate the statement format type (PDF, CSV, etc.) and language.

Text: {statement_text}

Response format:
{
  "bank_name": "Bank Name",
  "country": "Country",
  "format_type": "PDF/CSV/etc",
  "language": "Language",
  "confidence": 0.95
}
```

Additional Features to Consider

1. Multi-currency support
2. Transaction categorization
3. Spending analysis
4. Budget tracking integration
5. Mobile app companion
6. Cloud storage integration
7. Automated scheduling
8. Email notifications
9. Data backup and recovery
10. Multi-user support

This application should be production-ready with proper error handling, logging, documentation, and user experience considerations.
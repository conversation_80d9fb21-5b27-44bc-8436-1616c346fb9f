"""
Configuration management for bank statement processor.
Supports environment variables, config files, and command line arguments.
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List
from pydantic import BaseSettings, Field, validator
from dotenv import load_dotenv


class Settings(BaseSettings):
    """Main configuration class using Pydantic BaseSettings."""
    
    # AI Provider Configuration
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(None, env="ANTHROPIC_API_KEY")
    google_api_key: Optional[str] = Field(None, env="GOOGLE_API_KEY")
    mistral_api_key: Optional[str] = Field(None, env="MISTRAL_API_KEY")
    deepseek_api_key: Optional[str] = Field(None, env="DEEPSEEK_API_KEY")
    openrouter_api_key: Optional[str] = Field(None, env="OPENROUTER_API_KEY")
    custom_llm_endpoint: Optional[str] = Field(None, env="CUSTOM_LLM_ENDPOINT")
    custom_llm_api_key: Optional[str] = Field(None, env="CUSTOM_LLM_API_KEY")
    
    # Default AI Provider
    default_ai_provider: str = Field("openai", env="DEFAULT_AI_PROVIDER")
    
    # AI Model Configuration
    openai_model: str = Field("gpt-4", env="OPENAI_MODEL")
    anthropic_model: str = Field("claude-3-sonnet-20240229", env="ANTHROPIC_MODEL")
    google_model: str = Field("gemini-pro", env="GOOGLE_MODEL")
    mistral_model: str = Field("mistral-large-latest", env="MISTRAL_MODEL")
    deepseek_model: str = Field("deepseek-chat", env="DEEPSEEK_MODEL")
    openrouter_model: str = Field("anthropic/claude-3-sonnet", env="OPENROUTER_MODEL")
    
    # AI Parameters
    ai_temperature: float = Field(0.1, env="AI_TEMPERATURE")
    ai_max_tokens: int = Field(4000, env="AI_MAX_TOKENS")
    ai_timeout: int = Field(30, env="AI_TIMEOUT")
    
    # File Processing Configuration
    max_file_size_mb: int = Field(50, env="MAX_FILE_SIZE_MB")
    supported_formats: str = Field("pdf,csv,xlsx,xls,txt,jpg,jpeg,png", env="SUPPORTED_FORMATS")
    ocr_language: str = Field("eng", env="OCR_LANGUAGE")
    enable_ocr: bool = Field(True, env="ENABLE_OCR")
    
    # Output Configuration
    output_format: str = Field("xlsx", env="OUTPUT_FORMAT")
    separate_banks: bool = Field(True, env="SEPARATE_BANKS")
    monthly_sheets: bool = Field(True, env="MONTHLY_SHEETS")
    include_summary: bool = Field(True, env="INCLUDE_SUMMARY")
    
    # Logging Configuration
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_file: str = Field("logs/app.log", env="LOG_FILE")
    log_max_size_mb: int = Field(10, env="LOG_MAX_SIZE_MB")
    log_backup_count: int = Field(5, env="LOG_BACKUP_COUNT")
    
    # Processing Configuration
    batch_size: int = Field(10, env="BATCH_SIZE")
    max_concurrent_files: int = Field(3, env="MAX_CONCURRENT_FILES")
    retry_attempts: int = Field(3, env="RETRY_ATTEMPTS")
    retry_delay: int = Field(1, env="RETRY_DELAY")
    
    # Paths Configuration
    statements_path: str = Field("statements", env="STATEMENTS_PATH")
    processed_path: str = Field("processed", env="PROCESSED_PATH")
    output_path: str = Field("output", env="OUTPUT_PATH")
    logs_path: str = Field("logs", env="LOGS_PATH")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    @validator("default_ai_provider")
    def validate_ai_provider(cls, v):
        valid_providers = ["openai", "anthropic", "google", "mistral", "deepseek", "openrouter", "custom"]
        if v not in valid_providers:
            raise ValueError(f"Invalid AI provider. Must be one of: {valid_providers}")
        return v
    
    @validator("log_level")
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level. Must be one of: {valid_levels}")
        return v.upper()
    
    @property
    def supported_formats_list(self) -> List[str]:
        """Return supported formats as a list."""
        return [fmt.strip().lower() for fmt in self.supported_formats.split(",")]
    
    def get_api_key(self, provider: str) -> Optional[str]:
        """Get API key for a specific provider."""
        key_mapping = {
            "openai": self.openai_api_key,
            "anthropic": self.anthropic_api_key,
            "google": self.google_api_key,
            "mistral": self.mistral_api_key,
            "deepseek": self.deepseek_api_key,
            "openrouter": self.openrouter_api_key,
            "custom": self.custom_llm_api_key,
        }
        return key_mapping.get(provider)
    
    def get_model_name(self, provider: str) -> str:
        """Get model name for a specific provider."""
        model_mapping = {
            "openai": self.openai_model,
            "anthropic": self.anthropic_model,
            "google": self.google_model,
            "mistral": self.mistral_model,
            "deepseek": self.deepseek_model,
            "openrouter": self.openrouter_model,
        }
        return model_mapping.get(provider, "default")
    
    def validate_provider_config(self, provider: str) -> bool:
        """Validate that a provider has the necessary configuration."""
        api_key = self.get_api_key(provider)
        if not api_key:
            return False
        
        if provider == "custom" and not self.custom_llm_endpoint:
            return False
        
        return True


class ConfigManager:
    """Configuration manager with support for multiple configuration sources."""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file
        self.settings: Optional[Settings] = None
        self._load_config()
    
    def _load_config(self):
        """Load configuration from various sources."""
        # Load environment variables from .env file
        env_file = Path(".env")
        if env_file.exists():
            load_dotenv(env_file)
        
        # Load from config file if provided
        config_data = {}
        if self.config_file and Path(self.config_file).exists():
            config_data = self._load_config_file(self.config_file)
        
        # Create settings instance
        self.settings = Settings(**config_data)
    
    def _load_config_file(self, config_file: str) -> Dict[str, Any]:
        """Load configuration from JSON or YAML file."""
        config_path = Path(config_file)
        if not config_path.exists():
            return {}
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    return yaml.safe_load(f) or {}
                elif config_path.suffix.lower() == '.json':
                    return json.load(f) or {}
                else:
                    raise ValueError(f"Unsupported config file format: {config_path.suffix}")
        except Exception as e:
            print(f"Error loading config file {config_file}: {e}")
            return {}
    
    def get_settings(self) -> Settings:
        """Get the current settings instance."""
        if self.settings is None:
            self._load_config()
        return self.settings
    
    def reload_config(self):
        """Reload configuration from all sources."""
        self._load_config()
    
    def validate_all_providers(self) -> Dict[str, bool]:
        """Validate configuration for all providers."""
        providers = ["openai", "anthropic", "google", "mistral", "deepseek", "openrouter", "custom"]
        return {
            provider: self.settings.validate_provider_config(provider)
            for provider in providers
        }


# Global configuration instance
config_manager = ConfigManager()


def get_settings() -> Settings:
    """Get the global settings instance."""
    return config_manager.get_settings()


def reload_settings():
    """Reload the global settings."""
    config_manager.reload_config()

"""
Transaction data model for bank statement processing.
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator, root_validator
from enum import Enum
import re


class TransactionType(str, Enum):
    """Transaction type enumeration."""
    DEBIT = "debit"
    CREDIT = "credit"
    TRANSFER = "transfer"
    FEE = "fee"
    INTEREST = "interest"
    UNKNOWN = "unknown"


class TransactionStatus(str, Enum):
    """Transaction processing status."""
    RAW = "raw"
    PROCESSED = "processed"
    VALIDATED = "validated"
    ERROR = "error"


class Transaction(BaseModel):
    """Transaction data model with validation."""

    # Core transaction data
    date: date
    description: str
    debit: Optional[Decimal] = None
    credit: Optional[Decimal] = None
    balance: Optional[Decimal] = None

    # Additional metadata
    transaction_type: TransactionType = TransactionType.UNKNOWN
    reference: Optional[str] = None
    category: Optional[str] = None

    # Processing metadata
    status: TransactionStatus = TransactionStatus.RAW
    original_text: Optional[str] = None
    confidence_score: Optional[float] = None

    # Bank and statement metadata
    bank_name: Optional[str] = None
    account_number: Optional[str] = None
    statement_period: Optional[str] = None
    
    class Config:
        use_enum_values = True
        json_encoders = {
            Decimal: str,
            date: lambda v: v.isoformat(),
        }
    
    @root_validator
    def validate_amounts(cls, values):
        """Validate that either debit or credit is provided, but not both."""
        debit = values.get('debit')
        credit = values.get('credit')
        
        if debit is not None and credit is not None:
            if debit > 0 and credit > 0:
                raise ValueError("Transaction cannot have both debit and credit amounts")
        
        if debit is None and credit is None:
            raise ValueError("Transaction must have either debit or credit amount")
        
        return values
    
    @validator('description')
    def clean_description(cls, v):
        """Clean and normalize transaction description."""
        if not v:
            raise ValueError("Description cannot be empty")
        
        # Remove extra whitespace and normalize
        cleaned = re.sub(r'\s+', ' ', v.strip())
        
        # Remove common noise patterns
        noise_patterns = [
            r'\*+',  # Multiple asterisks
            r'#+',   # Multiple hash symbols
            r'=+',   # Multiple equals signs
        ]
        
        for pattern in noise_patterns:
            cleaned = re.sub(pattern, '', cleaned)
        
        return cleaned.strip()
    
    @validator('debit', 'credit', 'balance', pre=True)
    def parse_decimal(cls, v):
        """Parse decimal values from various formats."""
        if v is None:
            return None
        
        if isinstance(v, (int, float)):
            return Decimal(str(v))
        
        if isinstance(v, str):
            # Remove currency symbols and formatting
            cleaned = re.sub(r'[^\d.-]', '', v.replace(',', ''))
            if cleaned:
                try:
                    return Decimal(cleaned)
                except:
                    return None
        
        return v
    
    @property
    def amount(self) -> Decimal:
        """Get the transaction amount (positive for credit, negative for debit)."""
        if self.credit is not None:
            return self.credit
        elif self.debit is not None:
            return -self.debit
        else:
            return Decimal('0')
    
    @property
    def is_debit(self) -> bool:
        """Check if transaction is a debit."""
        return self.debit is not None and self.debit > 0
    
    @property
    def is_credit(self) -> bool:
        """Check if transaction is a credit."""
        return self.credit is not None and self.credit > 0
    
    def to_excel_row(self) -> List[Any]:
        """Convert transaction to Excel row format."""
        return [
            self.date.strftime('%d/%m/%Y'),
            self.description,
            float(self.debit) if self.debit else None,
            float(self.credit) if self.credit else None,
            float(self.balance) if self.balance else None
        ]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with proper serialization."""
        return {
            'date': self.date.isoformat(),
            'description': self.description,
            'debit': float(self.debit) if self.debit else None,
            'credit': float(self.credit) if self.credit else None,
            'balance': float(self.balance) if self.balance else None,
            'transaction_type': self.transaction_type,
            'reference': self.reference,
            'category': self.category,
            'status': self.status,
            'confidence_score': self.confidence_score,
            'bank_name': self.bank_name,
            'account_number': self.account_number,
            'statement_period': self.statement_period
        }


class TransactionBatch(BaseModel):
    """A batch of transactions from a single statement."""

    transactions: List[Transaction] = []

    def __init__(self, **data):
        if 'transactions' not in data:
            data['transactions'] = []
        super().__init__(**data)
    bank_name: Optional[str] = None
    account_number: Optional[str] = None
    statement_period: Optional[str] = None
    statement_date: Optional[date] = None
    opening_balance: Optional[Decimal] = None
    closing_balance: Optional[Decimal] = None
    total_debits: Optional[Decimal] = None
    total_credits: Optional[Decimal] = None
    
    # Processing metadata
    source_file: Optional[str] = None
    processed_at: Optional[datetime] = None
    processing_time: Optional[float] = None
    ai_provider: Optional[str] = None
    confidence_score: Optional[float] = None
    
    class Config:
        json_encoders = {
            Decimal: str,
            date: lambda v: v.isoformat(),
            datetime: lambda v: v.isoformat(),
        }
    
    def add_transaction(self, transaction: Transaction):
        """Add a transaction to the batch."""
        # Set batch metadata on transaction
        if self.bank_name:
            transaction.bank_name = self.bank_name
        if self.account_number:
            transaction.account_number = self.account_number
        if self.statement_period:
            transaction.statement_period = self.statement_period
        
        self.transactions.append(transaction)
    
    def calculate_totals(self):
        """Calculate total debits and credits."""
        self.total_debits = sum(
            t.debit for t in self.transactions 
            if t.debit is not None
        ) or Decimal('0')
        
        self.total_credits = sum(
            t.credit for t in self.transactions 
            if t.credit is not None
        ) or Decimal('0')
    
    def validate_balance(self) -> bool:
        """Validate that calculated balance matches closing balance."""
        if not self.opening_balance or not self.closing_balance:
            return True  # Cannot validate without both balances
        
        calculated_balance = self.opening_balance + self.total_credits - self.total_debits
        return abs(calculated_balance - self.closing_balance) < Decimal('0.01')
    
    def get_transactions_by_month(self) -> Dict[str, List[Transaction]]:
        """Group transactions by month."""
        monthly_transactions = {}
        
        for transaction in self.transactions:
            month_key = transaction.date.strftime('%Y-%m')
            if month_key not in monthly_transactions:
                monthly_transactions[month_key] = []
            monthly_transactions[month_key].append(transaction)
        
        return monthly_transactions
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """Get summary statistics for the batch."""
        return {
            'total_transactions': len(self.transactions),
            'total_debits': float(self.total_debits) if self.total_debits else 0,
            'total_credits': float(self.total_credits) if self.total_credits else 0,
            'date_range': {
                'start': min(t.date for t in self.transactions).isoformat() if self.transactions else None,
                'end': max(t.date for t in self.transactions).isoformat() if self.transactions else None
            },
            'balance_validated': self.validate_balance(),
            'processing_time': self.processing_time,
            'confidence_score': self.confidence_score
        }

"""
Main CLI application for bank statement processor.
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Dict, Any
import click
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.table import Table
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.text import Text
import time

# Import application modules
from config.settings import get_settings, reload_settings
from utils.logger import setup_logging, get_logger, get_processing_logger
from utils.file_handler import FileHand<PERSON>, DirectoryManager
from processors.statement_reader import statement_reader
from processors.ai_processor import ai_processor_manager
from processors.excel_generator import excel_generator
from models.transaction import TransactionBatch


class BankStatementProcessor:
    """Main application class."""
    
    def __init__(self):
        self.settings = get_settings()
        self.console = Console()
        self.logger = get_logger(__name__)
        self.processing_logger = get_processing_logger(__name__)
        
        # Initialize components
        self.file_handler = FileHandler(self.settings.max_file_size_mb)
        self.directory_manager = DirectoryManager(Path.cwd())
        
        # Ensure required directories exist
        self._ensure_directories()
        
        # Setup logging
        setup_logging(
            log_level=self.settings.log_level,
            log_file=self.settings.log_file,
            log_max_size_mb=self.settings.log_max_size_mb,
            log_backup_count=self.settings.log_backup_count
        )
    
    def _ensure_directories(self):
        """Ensure all required directories exist."""
        directories = [
            self.settings.statements_path,
            self.settings.processed_path,
            self.settings.output_path,
            self.settings.logs_path
        ]
        self.directory_manager.ensure_directories(directories)
    
    def display_welcome(self):
        """Display welcome message."""
        welcome_text = Text("AI-Powered Bank Statement Processor", style="bold blue")
        welcome_panel = Panel(
            welcome_text,
            subtitle="Process bank statements using AI and generate organized Excel reports",
            border_style="blue"
        )
        self.console.print(welcome_panel)
        self.console.print()
    
    def display_main_menu(self) -> str:
        """Display main menu and get user choice."""
        self.console.print("[bold cyan]Main Menu[/bold cyan]")
        self.console.print()
        
        options = [
            "1. Process all statements",
            "2. Process specific bank statements", 
            "3. Configure AI providers",
            "4. View processing history",
            "5. Generate reports",
            "6. Settings",
            "7. Exit"
        ]
        
        for option in options:
            self.console.print(f"  {option}")
        
        self.console.print()
        choice = Prompt.ask("Select an option", choices=["1", "2", "3", "4", "5", "6", "7"])
        return choice
    
    async def process_all_statements(self):
        """Process all statements in the statements directory."""
        self.console.print("[bold green]Processing All Statements[/bold green]")
        self.console.print()
        
        # Scan for files
        statements_dir = Path(self.settings.statements_path)
        if not statements_dir.exists():
            self.console.print(f"[red]Statements directory not found: {statements_dir}[/red]")
            return
        
        files = self.file_handler.scan_directory(statements_dir)
        if not files:
            self.console.print("[yellow]No supported files found in statements directory[/yellow]")
            return
        
        # Display file summary
        self._display_file_summary(files)
        
        if not Confirm.ask("Proceed with processing?"):
            return
        
        # Process files
        await self._process_files([f['path'] for f in files])
    
    async def process_specific_bank(self):
        """Process statements for a specific bank."""
        self.console.print("[bold green]Process Specific Bank Statements[/bold green]")
        self.console.print()
        
        # Get available files
        statements_dir = Path(self.settings.statements_path)
        files = self.file_handler.scan_directory(statements_dir)
        
        if not files:
            self.console.print("[yellow]No files found in statements directory[/yellow]")
            return
        
        # Display files for selection
        self.console.print("Available files:")
        for i, file_info in enumerate(files, 1):
            self.console.print(f"  {i}. {file_info['name']} ({file_info['size']} bytes)")
        
        self.console.print()
        selection = Prompt.ask("Enter file numbers (comma-separated) or 'all'")
        
        if selection.lower() == 'all':
            selected_files = [f['path'] for f in files]
        else:
            try:
                indices = [int(x.strip()) - 1 for x in selection.split(',')]
                selected_files = [files[i]['path'] for i in indices if 0 <= i < len(files)]
            except (ValueError, IndexError):
                self.console.print("[red]Invalid selection[/red]")
                return
        
        if not selected_files:
            self.console.print("[red]No files selected[/red]")
            return
        
        await self._process_files(selected_files)
    
    async def _process_files(self, file_paths: List[Path]):
        """Process a list of files."""
        self.processing_logger.start_processing()
        
        transaction_batches = []
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console
        ) as progress:
            
            main_task = progress.add_task("Processing files...", total=len(file_paths))
            
            for file_path in file_paths:
                file_task = progress.add_task(f"Processing {file_path.name}...", total=100)
                
                try:
                    # Update progress
                    progress.update(file_task, advance=20, description=f"Reading {file_path.name}...")
                    
                    # Read file
                    read_result = statement_reader.read_statement(file_path)
                    if not read_result['success']:
                        self.processing_logger.log_file_error(str(file_path), read_result['error'])
                        progress.update(file_task, completed=100, description=f"Failed: {file_path.name}")
                        continue
                    
                    progress.update(file_task, advance=30, description=f"Identifying bank...")
                    
                    # Identify bank
                    bank_info = await ai_processor_manager.identify_bank_with_fallback(
                        read_result['text']
                    )
                    
                    progress.update(file_task, advance=20, description=f"Extracting transactions...")
                    
                    # Extract transactions
                    batch = await ai_processor_manager.extract_transactions_with_fallback(
                        read_result['text']
                    )
                    
                    # Set bank info
                    batch.bank_name = bank_info.get('bank_name', 'Unknown')
                    batch.source_file = str(file_path)
                    
                    transaction_batches.append(batch)
                    
                    progress.update(file_task, advance=30, description=f"Completed: {file_path.name}")
                    
                    self.processing_logger.log_file_success(
                        str(file_path), 
                        len(batch.transactions),
                        time.time()  # Simplified timing
                    )
                
                except Exception as e:
                    self.processing_logger.log_file_error(str(file_path), str(e))
                    progress.update(file_task, completed=100, description=f"Error: {file_path.name}")
                    self.logger.error(f"Error processing {file_path}: {e}")
                
                progress.update(main_task, advance=1)
        
        # Generate Excel output
        if transaction_batches:
            await self._generate_output(transaction_batches)
        
        self.processing_logger.end_processing()
        
        # Display summary
        self._display_processing_summary(transaction_batches)
    
    async def _generate_output(self, transaction_batches: List[TransactionBatch]):
        """Generate Excel output from transaction batches."""
        self.console.print("\n[bold cyan]Generating Excel Reports...[/bold cyan]")
        
        output_dir = Path(self.settings.output_path)
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            
            task = progress.add_task("Generating Excel files...", total=None)
            
            result = excel_generator.generate_workbook(
                transaction_batches,
                output_dir,
                self.settings.separate_banks,
                self.settings.monthly_sheets
            )
            
            progress.update(task, completed=100, description="Excel generation completed")
        
        if result['success']:
            self.console.print(f"[green]✓ Generated {len(result['files_created'])} Excel file(s)[/green]")
            for file_path in result['files_created']:
                self.console.print(f"  • {file_path}")
        else:
            self.console.print(f"[red]✗ Excel generation failed: {result['error']}[/red]")
    
    def _display_file_summary(self, files: List[Dict[str, Any]]):
        """Display summary of files to be processed."""
        table = Table(title="Files to Process")
        table.add_column("File Name", style="cyan")
        table.add_column("Type", style="magenta")
        table.add_column("Size", style="green")
        
        for file_info in files:
            size_mb = file_info['size'] / (1024 * 1024)
            table.add_row(
                file_info['name'],
                file_info['type'],
                f"{size_mb:.2f} MB"
            )
        
        self.console.print(table)
        self.console.print()
    
    def _display_processing_summary(self, transaction_batches: List[TransactionBatch]):
        """Display processing summary."""
        self.console.print("\n[bold cyan]Processing Summary[/bold cyan]")
        
        total_transactions = sum(len(batch.transactions) for batch in transaction_batches)
        successful_batches = len([b for b in transaction_batches if b.transactions])
        
        summary_table = Table()
        summary_table.add_column("Metric", style="cyan")
        summary_table.add_column("Value", style="green")
        
        summary_table.add_row("Files Processed", str(len(transaction_batches)))
        summary_table.add_row("Successful Batches", str(successful_batches))
        summary_table.add_row("Total Transactions", str(total_transactions))
        
        # AI usage stats
        usage_stats = ai_processor_manager.get_usage_stats()
        summary_table.add_row("AI Requests", str(usage_stats['total_requests']))
        summary_table.add_row("Total Cost", f"${usage_stats['total_cost']:.4f}")
        
        self.console.print(summary_table)
        self.console.print()

    def configure_ai_providers(self):
        """Configure AI providers."""
        self.console.print("[bold green]Configure AI Providers[/bold green]")
        self.console.print()

        # Display current providers
        available_providers = ai_processor_manager.processor.get_available_providers()

        if available_providers:
            self.console.print("[green]Available Providers:[/green]")
            for provider in available_providers:
                self.console.print(f"  ✓ {provider}")
        else:
            self.console.print("[red]No providers configured[/red]")

        self.console.print()
        self.console.print("To configure providers, edit the .env file with your API keys.")
        self.console.print("Then restart the application.")

        if Confirm.ask("Open configuration guide?"):
            self._show_configuration_guide()

    def _show_configuration_guide(self):
        """Show configuration guide."""
        guide_text = """
Configuration Guide:

1. Copy .env.example to .env
2. Add your API keys:
   - OPENAI_API_KEY=your_key_here
   - ANTHROPIC_API_KEY=your_key_here
   - GOOGLE_API_KEY=your_key_here
   - etc.

3. Set your preferred default provider:
   - DEFAULT_AI_PROVIDER=openai

4. Adjust other settings as needed
5. Restart the application
        """

        panel = Panel(guide_text.strip(), title="Configuration Guide", border_style="blue")
        self.console.print(panel)

    def view_processing_history(self):
        """View processing history."""
        self.console.print("[bold green]Processing History[/bold green]")
        self.console.print()

        # Check for log files
        logs_dir = Path(self.settings.logs_path)
        if not logs_dir.exists():
            self.console.print("[yellow]No log directory found[/yellow]")
            return

        log_files = list(logs_dir.glob("*.log"))
        if not log_files:
            self.console.print("[yellow]No log files found[/yellow]")
            return

        # Display recent log entries (simplified)
        latest_log = max(log_files, key=lambda f: f.stat().st_mtime)

        self.console.print(f"[cyan]Latest log file: {latest_log.name}[/cyan]")
        self.console.print()

        try:
            with open(latest_log, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                recent_lines = lines[-20:]  # Show last 20 lines

                for line in recent_lines:
                    if 'ERROR' in line:
                        self.console.print(f"[red]{line.strip()}[/red]")
                    elif 'WARNING' in line:
                        self.console.print(f"[yellow]{line.strip()}[/yellow]")
                    elif 'INFO' in line:
                        self.console.print(f"[green]{line.strip()}[/green]")
                    else:
                        self.console.print(line.strip())

        except Exception as e:
            self.console.print(f"[red]Error reading log file: {e}[/red]")

    def generate_reports(self):
        """Generate additional reports."""
        self.console.print("[bold green]Generate Reports[/bold green]")
        self.console.print()

        # Check for processed data
        output_dir = Path(self.settings.output_path)
        if not output_dir.exists():
            self.console.print("[yellow]No output directory found[/yellow]")
            return

        excel_files = list(output_dir.glob("*.xlsx"))
        if not excel_files:
            self.console.print("[yellow]No Excel files found. Process some statements first.[/yellow]")
            return

        self.console.print("Available Excel files:")
        for i, file_path in enumerate(excel_files, 1):
            self.console.print(f"  {i}. {file_path.name}")

        self.console.print()
        self.console.print("Additional report formats:")
        self.console.print("  1. Export to CSV")
        self.console.print("  2. Export to JSON")
        self.console.print("  3. Generate summary report")

        choice = Prompt.ask("Select option", choices=["1", "2", "3"])

        if choice == "1":
            self._export_to_csv()
        elif choice == "2":
            self._export_to_json()
        elif choice == "3":
            self._generate_summary_report()

    def _export_to_csv(self):
        """Export data to CSV format."""
        self.console.print("[yellow]CSV export functionality would be implemented here[/yellow]")
        # This would require loading Excel data and converting to CSV

    def _export_to_json(self):
        """Export data to JSON format."""
        self.console.print("[yellow]JSON export functionality would be implemented here[/yellow]")
        # This would require loading Excel data and converting to JSON

    def _generate_summary_report(self):
        """Generate summary report."""
        self.console.print("[yellow]Summary report generation would be implemented here[/yellow]")
        # This would analyze the processed data and generate insights

    def show_settings(self):
        """Show current settings."""
        self.console.print("[bold green]Current Settings[/bold green]")
        self.console.print()

        settings_table = Table()
        settings_table.add_column("Setting", style="cyan")
        settings_table.add_column("Value", style="green")

        # Display key settings
        settings_table.add_row("Default AI Provider", self.settings.default_ai_provider)
        settings_table.add_row("Max File Size (MB)", str(self.settings.max_file_size_mb))
        settings_table.add_row("Separate Banks", str(self.settings.separate_banks))
        settings_table.add_row("Monthly Sheets", str(self.settings.monthly_sheets))
        settings_table.add_row("OCR Enabled", str(self.settings.enable_ocr))
        settings_table.add_row("Log Level", self.settings.log_level)

        self.console.print(settings_table)
        self.console.print()

        if Confirm.ask("Reload settings from .env file?"):
            reload_settings()
            self.settings = get_settings()
            self.console.print("[green]Settings reloaded[/green]")


@click.command()
@click.option('--config', '-c', help='Configuration file path')
@click.option('--batch', '-b', is_flag=True, help='Run in batch mode (no interactive menu)')
@click.option('--input-dir', '-i', help='Input directory for statements')
@click.option('--output-dir', '-o', help='Output directory for Excel files')
def main(config, batch, input_dir, output_dir):
    """AI-Powered Bank Statement Processor CLI."""

    try:
        # Initialize processor
        processor = BankStatementProcessor()

        # Override settings if provided
        if input_dir:
            processor.settings.statements_path = input_dir
        if output_dir:
            processor.settings.output_path = output_dir

        if batch:
            # Run in batch mode
            asyncio.run(processor.process_all_statements())
        else:
            # Run interactive mode
            processor.display_welcome()

            while True:
                try:
                    choice = processor.display_main_menu()

                    if choice == "1":
                        asyncio.run(processor.process_all_statements())
                    elif choice == "2":
                        asyncio.run(processor.process_specific_bank())
                    elif choice == "3":
                        processor.configure_ai_providers()
                    elif choice == "4":
                        processor.view_processing_history()
                    elif choice == "5":
                        processor.generate_reports()
                    elif choice == "6":
                        processor.show_settings()
                    elif choice == "7":
                        processor.console.print("[cyan]Thank you for using Bank Statement Processor![/cyan]")
                        break

                    # Pause before showing menu again
                    if choice != "7":
                        processor.console.print()
                        Prompt.ask("Press Enter to continue")
                        processor.console.clear()

                except KeyboardInterrupt:
                    processor.console.print("\n[yellow]Operation cancelled by user[/yellow]")
                    break
                except Exception as e:
                    processor.console.print(f"[red]Unexpected error: {e}[/red]")
                    processor.logger.error(f"Unexpected error in main loop: {e}")

    except Exception as e:
        console = Console()
        console.print(f"[red]Fatal error: {e}[/red]")
        sys.exit(1)


if __name__ == "__main__":
    main()

"""
File handling utilities for bank statement processor.
"""

import os
import shutil
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple
import mimetypes
import hashlib
from datetime import datetime
import chardet


class FileHandler:
    """Utility class for file operations."""
    
    def __init__(self, max_file_size_mb: int = 50):
        self.max_file_size_bytes = max_file_size_mb * 1024 * 1024
        self.supported_extensions = {
            '.pdf': 'application/pdf',
            '.csv': 'text/csv',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.xls': 'application/vnd.ms-excel',
            '.txt': 'text/plain',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png'
        }
    
    def validate_file(self, file_path: Path) -> Dict[str, Any]:
        """Validate file for processing."""
        result = {
            'valid': False,
            'file_path': str(file_path),
            'file_name': file_path.name,
            'file_size': 0,
            'file_type': None,
            'mime_type': None,
            'encoding': None,
            'errors': []
        }
        
        try:
            # Check if file exists
            if not file_path.exists():
                result['errors'].append(f"File does not exist: {file_path}")
                return result
            
            # Check if it's a file (not directory)
            if not file_path.is_file():
                result['errors'].append(f"Path is not a file: {file_path}")
                return result
            
            # Get file size
            file_size = file_path.stat().st_size
            result['file_size'] = file_size
            
            # Check file size
            if file_size > self.max_file_size_bytes:
                result['errors'].append(f"File too large: {file_size / (1024*1024):.1f}MB > {self.max_file_size_bytes / (1024*1024)}MB")
                return result
            
            # Check file extension
            file_extension = file_path.suffix.lower()
            if file_extension not in self.supported_extensions:
                result['errors'].append(f"Unsupported file type: {file_extension}")
                return result
            
            result['file_type'] = file_extension
            result['mime_type'] = self.supported_extensions[file_extension]
            
            # Detect encoding for text files
            if file_extension in ['.txt', '.csv']:
                encoding = self.detect_encoding(file_path)
                result['encoding'] = encoding
            
            # Verify MIME type
            detected_mime, _ = mimetypes.guess_type(str(file_path))
            if detected_mime and detected_mime != result['mime_type']:
                result['errors'].append(f"MIME type mismatch: expected {result['mime_type']}, got {detected_mime}")
            
            result['valid'] = len(result['errors']) == 0
            
        except Exception as e:
            result['errors'].append(f"Error validating file: {str(e)}")
        
        return result
    
    def detect_encoding(self, file_path: Path) -> str:
        """Detect file encoding."""
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # Read first 10KB
                result = chardet.detect(raw_data)
                return result.get('encoding', 'utf-8')
        except Exception:
            return 'utf-8'
    
    def read_text_file(self, file_path: Path, encoding: Optional[str] = None) -> str:
        """Read text file with proper encoding detection."""
        if encoding is None:
            encoding = self.detect_encoding(file_path)
        
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
        except UnicodeDecodeError:
            # Fallback to utf-8 with error handling
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                return f.read()
    
    def calculate_file_hash(self, file_path: Path) -> str:
        """Calculate MD5 hash of file."""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""
    
    def move_file(self, source: Path, destination: Path) -> bool:
        """Move file to destination directory."""
        try:
            destination.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(source), str(destination))
            return True
        except Exception as e:
            print(f"Error moving file {source} to {destination}: {e}")
            return False
    
    def copy_file(self, source: Path, destination: Path) -> bool:
        """Copy file to destination directory."""
        try:
            destination.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(str(source), str(destination))
            return True
        except Exception as e:
            print(f"Error copying file {source} to {destination}: {e}")
            return False
    
    def create_backup(self, file_path: Path, backup_dir: Path) -> Optional[Path]:
        """Create a backup of the file."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
            backup_path = backup_dir / backup_name
            
            if self.copy_file(file_path, backup_path):
                return backup_path
        except Exception as e:
            print(f"Error creating backup for {file_path}: {e}")
        
        return None
    
    def scan_directory(self, directory: Path, recursive: bool = True) -> List[Dict[str, Any]]:
        """Scan directory for supported files."""
        files = []
        
        try:
            if recursive:
                pattern = "**/*"
            else:
                pattern = "*"
            
            for file_path in directory.glob(pattern):
                if file_path.is_file():
                    validation_result = self.validate_file(file_path)
                    if validation_result['valid']:
                        files.append({
                            'path': file_path,
                            'name': file_path.name,
                            'size': validation_result['file_size'],
                            'type': validation_result['file_type'],
                            'mime_type': validation_result['mime_type'],
                            'encoding': validation_result.get('encoding'),
                            'hash': self.calculate_file_hash(file_path)
                        })
        
        except Exception as e:
            print(f"Error scanning directory {directory}: {e}")
        
        return files
    
    def organize_files_by_type(self, files: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Organize files by their type."""
        organized = {}
        
        for file_info in files:
            file_type = file_info['type']
            if file_type not in organized:
                organized[file_type] = []
            organized[file_type].append(file_info)
        
        return organized
    
    def get_file_stats(self, files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get statistics about the files."""
        if not files:
            return {
                'total_files': 0,
                'total_size': 0,
                'types': {},
                'average_size': 0
            }
        
        total_size = sum(f['size'] for f in files)
        types = {}
        
        for file_info in files:
            file_type = file_info['type']
            if file_type not in types:
                types[file_type] = {'count': 0, 'size': 0}
            types[file_type]['count'] += 1
            types[file_type]['size'] += file_info['size']
        
        return {
            'total_files': len(files),
            'total_size': total_size,
            'types': types,
            'average_size': total_size / len(files) if files else 0
        }
    
    def cleanup_temp_files(self, temp_dir: Path, max_age_hours: int = 24):
        """Clean up temporary files older than specified hours."""
        try:
            if not temp_dir.exists():
                return
            
            current_time = datetime.now().timestamp()
            max_age_seconds = max_age_hours * 3600
            
            for file_path in temp_dir.rglob('*'):
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        try:
                            file_path.unlink()
                        except Exception as e:
                            print(f"Error deleting temp file {file_path}: {e}")
        
        except Exception as e:
            print(f"Error cleaning up temp files: {e}")


class DirectoryManager:
    """Utility class for directory management."""
    
    def __init__(self, base_path: Path):
        self.base_path = Path(base_path)
    
    def ensure_directories(self, directories: List[str]):
        """Ensure all required directories exist."""
        for directory in directories:
            dir_path = self.base_path / directory
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def get_directory_size(self, directory: Path) -> int:
        """Get total size of directory."""
        total_size = 0
        try:
            for file_path in directory.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception as e:
            print(f"Error calculating directory size: {e}")
        
        return total_size
    
    def archive_old_files(self, source_dir: Path, archive_dir: Path, days_old: int = 30):
        """Archive files older than specified days."""
        try:
            current_time = datetime.now().timestamp()
            cutoff_time = current_time - (days_old * 24 * 3600)
            
            archive_dir.mkdir(parents=True, exist_ok=True)
            
            for file_path in source_dir.rglob('*'):
                if file_path.is_file():
                    if file_path.stat().st_mtime < cutoff_time:
                        relative_path = file_path.relative_to(source_dir)
                        archive_path = archive_dir / relative_path
                        archive_path.parent.mkdir(parents=True, exist_ok=True)
                        
                        try:
                            shutil.move(str(file_path), str(archive_path))
                        except Exception as e:
                            print(f"Error archiving file {file_path}: {e}")
        
        except Exception as e:
            print(f"Error archiving old files: {e}")
